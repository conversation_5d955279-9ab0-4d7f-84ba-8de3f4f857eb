# YeehawVideo 🤠📹

A GPS-aware video compressor for macOS that preserves and reinjects GPS metadata while efficiently compressing your videos.

## Features

### Core Functionality
- **Video Compression**: Uses FFmpeg with H.264 encoding (CRF 28) for optimal file size reduction
- **GPS Metadata Preservation**: Automatically detects, extracts, and preserves GPS location data
- **Smart Output Format**: Outputs .mov files when GPS data is present, .mp4 otherwise
- **Batch Processing**: Process single files or entire folders of videos
- **Automatic Tool Installation**: Installs required dependencies (Homebrew, ffmpeg, jq, exiftool) on first run

### Supported Formats
- Input: .mov, .mp4, .webm, .avi, .mkv
- Output: .mov (with GPS), .mp4 (without GPS)

### GPS Metadata Features
- **Detection**: Finds GPS data in various formats (ISO6709, latitude/longitude fields)
- **Export**: Saves GPS data to JSON sidecar files (`filename_gps.json`)
- **Injection**: Reinjects GPS metadata into compressed .mov files using exiftool
- **Preservation**: Maintains original GPS coordinates and references

## Requirements

- macOS Ventura 13.0 or later
- Apple Silicon or Intel Mac
- Internet connection for initial dependency installation

## Installation

1. Clone or download this repository
2. Open `YeehawVideo.xcodeproj` in Xcode
3. Build and run the project
4. On first launch, the app will prompt to install required dependencies

## Usage

1. **Launch YeehawVideo**
2. **Select Input**: Choose a single video file or folder containing videos
3. **Select Output**: Choose destination folder for compressed videos
4. **Options**: Toggle "Delete original files after compression" if desired
5. **Compress**: Click the Compress button to start processing
6. **Monitor Progress**: Watch real-time progress and view detailed logs
7. **Review Results**: Check compression statistics and any errors

## Output Files

For each processed video, YeehawVideo creates:
- **Compressed Video**: `originalname_zmt.mov` or `originalname_zmt.mp4`
- **GPS Sidecar** (if GPS detected): `originalname_gps.json`

## Technical Details

### Compression Settings
```bash
ffmpeg -i input.mov \
  -vcodec libx264 \
  -crf 28 \
  -preset fast \
  -pix_fmt yuv420p \
  -movflags +faststart \
  -r [original_frame_rate] \
  output_zmt.mov
```

### Dependencies
- **Homebrew**: Package manager for macOS
- **FFmpeg**: Video processing and compression
- **jq**: JSON processing for metadata
- **exiftool**: GPS metadata injection

### Architecture
- **SwiftUI**: Modern macOS native interface
- **Async/Await**: Non-blocking video processing
- **Error Handling**: Comprehensive error management and recovery
- **Batch Processing**: Efficient multi-file handling
- **Progress Tracking**: Real-time progress updates

## Error Handling

YeehawVideo includes robust error handling for:
- Missing dependencies
- Unsupported file formats
- Insufficient disk space
- Corrupted video files
- GPS metadata extraction failures
- File permission issues

## Logging

The app provides detailed logging for:
- Compression progress and results
- GPS metadata detection and injection
- Error messages and recovery suggestions
- Performance statistics

## Development

### Project Structure
```
YeehawVideo/
├── YeehawVideoApp.swift          # Main app entry point
├── ContentView.swift             # Primary UI
├── DependencyManager.swift       # CLI tool management
├── VideoCompressionService.swift # Core compression logic
├── GPSMetadataService.swift      # GPS handling
├── ErrorHandler.swift            # Error management
├── BatchProcessor.swift          # Multi-file processing
├── ResultsView.swift             # Results display
├── LogsView.swift                # Log viewer
└── YeehawVideo.entitlements      # App permissions
```

### Building
1. Open in Xcode 15.0+
2. Select your development team
3. Build for macOS (⌘+B)
4. Run (⌘+R)

## License

This project is open source. Feel free to modify and distribute.

## Contributing

Contributions welcome! Please feel free to submit pull requests or open issues.

---

**YeehawVideo** - Because your videos deserve to keep their sense of direction! 🤠🧭
