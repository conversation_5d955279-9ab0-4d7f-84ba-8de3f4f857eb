//
//  BatchProcessor.swift
//  YeehawVideo
//
//  Created by <PERSON> on 7/21/25.
//

import Foundation
import SwiftUI

struct BatchProcessingOptions {
    let inputPath: String
    let outputDirectory: String
    let deleteOriginal: Bool
    let maxConcurrentOperations: Int
    
    init(inputPath: String, outputDirectory: String, deleteOriginal: Bool = false, maxConcurrentOperations: Int = 1) {
        self.inputPath = inputPath
        self.outputDirectory = outputDirectory
        self.deleteOriginal = deleteOriginal
        self.maxConcurrentOperations = maxConcurrentOperations
    }
}

struct BatchProcessingProgress {
    let currentFileIndex: Int
    let totalFiles: Int
    let currentFileName: String
    let overallProgress: Double
    let estimatedTimeRemaining: TimeInterval?
    
    var progressText: String {
        return "Processing \(currentFileIndex + 1) of \(totalFiles): \(currentFileName)"
    }
}

@MainActor
class BatchProcessor: ObservableObject {
    @Published var isProcessing = false
    @Published var progress = BatchProcessingProgress(
        currentFileIndex: 0,
        totalFiles: 0,
        currentFileName: "",
        overallProgress: 0.0,
        estimatedTimeRemaining: nil
    )
    @Published var processingLog: [String] = []
    
    private let compressionService: VideoCompressionService
    private let gpsService: GPSMetadataService
    private let supportedExtensions = ["mov", "mp4", "webm", "avi", "mkv"]
    
    private var startTime: Date?
    private var processedFiles = 0
    
    init(compressionService: VideoCompressionService, gpsService: GPSMetadataService) {
        self.compressionService = compressionService
        self.gpsService = gpsService
    }
    
    func processBatch(with options: BatchProcessingOptions) async -> [CompressionResult] {
        isProcessing = true
        startTime = Date()
        processedFiles = 0
        processingLog.removeAll()
        
        addToLog("Starting batch processing...")
        addToLog("Input: \(options.inputPath)")
        addToLog("Output: \(options.outputDirectory)")
        addToLog("Delete originals: \(options.deleteOriginal)")
        
        // Discover files to process
        let filesToProcess = await discoverFiles(at: options.inputPath)
        addToLog("Found \(filesToProcess.count) video files to process")
        
        if filesToProcess.isEmpty {
            addToLog("No video files found to process")
            isProcessing = false
            return []
        }
        
        // Validate output directory
        guard await validateOutputDirectory(options.outputDirectory) else {
            addToLog("Error: Invalid output directory")
            isProcessing = false
            return []
        }
        
        // Check available disk space
        let requiredSpace = await estimateRequiredSpace(for: filesToProcess)
        let availableSpace = getAvailableDiskSpace(at: options.outputDirectory)
        
        if availableSpace < requiredSpace {
            addToLog("Warning: Insufficient disk space. Required: \(formatBytes(requiredSpace)), Available: \(formatBytes(availableSpace))")
        }
        
        var results: [CompressionResult] = []
        
        // Process files
        for (index, filePath) in filesToProcess.enumerated() {
            let fileName = URL(fileURLWithPath: filePath).lastPathComponent
            
            // Update progress
            progress = BatchProcessingProgress(
                currentFileIndex: index,
                totalFiles: filesToProcess.count,
                currentFileName: fileName,
                overallProgress: Double(index) / Double(filesToProcess.count),
                estimatedTimeRemaining: calculateEstimatedTimeRemaining(
                    currentIndex: index,
                    totalFiles: filesToProcess.count
                )
            )
            
            addToLog("Processing file \(index + 1)/\(filesToProcess.count): \(fileName)")
            
            // Process single file
            let result = await processSingleFile(
                filePath: filePath,
                outputDirectory: options.outputDirectory,
                deleteOriginal: options.deleteOriginal
            )
            
            results.append(result)
            processedFiles += 1
            
            if result.success {
                addToLog("✓ Successfully processed: \(fileName)")
                if let compressedSize = result.compressedSize {
                    let savings = result.originalSize - compressedSize
                    let ratio = Double(savings) / Double(result.originalSize) * 100
                    addToLog("  Space saved: \(formatBytes(savings)) (\(String(format: "%.1f", ratio))%)")
                }
            } else {
                addToLog("✗ Failed to process: \(fileName)")
                if let error = result.error {
                    addToLog("  Error: \(error)")
                }
            }
        }
        
        // Final progress update
        progress = BatchProcessingProgress(
            currentFileIndex: filesToProcess.count,
            totalFiles: filesToProcess.count,
            currentFileName: "",
            overallProgress: 1.0,
            estimatedTimeRemaining: 0
        )
        
        // Summary
        let successCount = results.filter { $0.success }.count
        let failureCount = results.count - successCount
        let totalOriginalSize = results.reduce(0) { $0 + $1.originalSize }
        let totalCompressedSize = results.compactMap { $0.compressedSize }.reduce(0, +)
        let totalSavings = totalOriginalSize - totalCompressedSize
        
        addToLog("Batch processing completed!")
        addToLog("Summary:")
        addToLog("  Total files: \(results.count)")
        addToLog("  Successful: \(successCount)")
        addToLog("  Failed: \(failureCount)")
        addToLog("  Original size: \(formatBytes(totalOriginalSize))")
        addToLog("  Compressed size: \(formatBytes(totalCompressedSize))")
        addToLog("  Space saved: \(formatBytes(totalSavings))")
        
        if let startTime = startTime {
            let duration = Date().timeIntervalSince(startTime)
            addToLog("  Processing time: \(formatDuration(duration))")
        }
        
        isProcessing = false
        return results
    }
    
    private func discoverFiles(at path: String) async -> [String] {
        var files: [String] = []

        var isDirectory: ObjCBool = false
        guard FileManager.default.fileExists(atPath: path, isDirectory: &isDirectory) else {
            return []
        }

        if isDirectory.boolValue {
            // Recursively find video files in directory
            files = await findVideoFiles(in: path)
        } else {
            // Single file
            let fileExtension = URL(fileURLWithPath: path).pathExtension.lowercased()
            if supportedExtensions.contains(fileExtension) {
                files = [path]
            }
        }

        return files.sorted()
    }
    
    private func findVideoFiles(in directoryPath: String) async -> [String] {
        return await withCheckedContinuation { continuation in
            Task.detached {
                var videoFiles: [String] = []

                guard let enumerator = FileManager.default.enumerator(atPath: directoryPath) else {
                    continuation.resume(returning: [])
                    return
                }

                while let fileName = enumerator.nextObject() as? String {
                    let fullPath = URL(fileURLWithPath: directoryPath).appendingPathComponent(fileName).path
                    let fileExtension = URL(fileURLWithPath: fullPath).pathExtension.lowercased()

                    if self.supportedExtensions.contains(fileExtension) {
                        videoFiles.append(fullPath)
                    }
                }

                continuation.resume(returning: videoFiles)
            }
        }
    }
    
    private func validateOutputDirectory(_ path: String) async -> Bool {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                var isDirectory: ObjCBool = false
                let exists = FileManager.default.fileExists(atPath: path, isDirectory: &isDirectory)
                
                if exists && isDirectory.boolValue {
                    // Check if directory is writable
                    let testFilePath = URL(fileURLWithPath: path).appendingPathComponent(".yeehaw_test").path
                    let testData = "test".data(using: .utf8)!
                    
                    do {
                        try testData.write(to: URL(fileURLWithPath: testFilePath))
                        try FileManager.default.removeItem(atPath: testFilePath)
                        continuation.resume(returning: true)
                    } catch {
                        continuation.resume(returning: false)
                    }
                } else {
                    continuation.resume(returning: false)
                }
            }
        }
    }
    
    private func estimateRequiredSpace(for files: [String]) async -> Int64 {
        return await withCheckedContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                let totalSize = files.reduce(0) { total, filePath in
                    do {
                        let attributes = try FileManager.default.attributesOfItem(atPath: filePath)
                        return total + Int(attributes[.size] as? Int64 ?? 0)
                    } catch {
                        return total
                    }
                }
                
                // Estimate compressed size as 30% of original (conservative estimate)
                let estimatedCompressedSize = Int64(Double(totalSize) * 0.3)
                continuation.resume(returning: estimatedCompressedSize)
            }
        }
    }
    
    private func getAvailableDiskSpace(at path: String) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfFileSystem(forPath: path)
            return attributes[.systemFreeSize] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
    
    private func processSingleFile(filePath: String, outputDirectory: String, deleteOriginal: Bool) async -> CompressionResult {
        // Extract GPS metadata first
        let gpsData = await gpsService.extractGPSMetadata(from: filePath)
        
        // Save GPS data to JSON if found
        if let gpsData = gpsData {
            _ = await gpsService.saveGPSToJSON(gpsData, for: filePath, in: outputDirectory)
        }
        
        // Compress the video
        let result = await compressionService.compressVideo(
            inputPath: filePath,
            outputDirectory: outputDirectory,
            deleteOriginal: deleteOriginal
        )
        
        // Inject GPS metadata if compression was successful and GPS data exists
        if result.success, let outputPath = result.outputPath, let gpsData = gpsData {
            _ = await gpsService.injectGPSMetadata(gpsData, into: outputPath)
        }
        
        return result
    }
    
    private func calculateEstimatedTimeRemaining(currentIndex: Int, totalFiles: Int) -> TimeInterval? {
        guard let startTime = startTime, currentIndex > 0 else { return nil }
        
        let elapsed = Date().timeIntervalSince(startTime)
        let averageTimePerFile = elapsed / Double(currentIndex)
        let remainingFiles = totalFiles - currentIndex
        
        return averageTimePerFile * Double(remainingFiles)
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) % 3600 / 60
        let seconds = Int(duration) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%d:%02d", minutes, seconds)
        }
    }
    
    private func addToLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        processingLog.append("[\(timestamp)] \(message)")
    }
}
