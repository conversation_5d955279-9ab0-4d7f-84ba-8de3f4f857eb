//
//  YeehawVideoApp.swift
//  YeehawVideo
//
//  Created by <PERSON> on 7/21/25.
//

import SwiftUI

@main
struct YeehawVideoApp: App {
    @StateObject private var errorHandler = ErrorHandler()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(errorHandler)
                .errorAlert(errorHandler)
        }
        .windowResizability(.contentSize)
        .windowStyle(.titleBar)
        .commands {
            CommandGroup(replacing: .help) {
                But<PERSON>("YeehawVideo Help") {
                    // Could open help documentation
                }
                .keyboardShortcut("?", modifiers: .command)
            }
        }
    }
}
