//
//  VideoCompressionService.swift
//  YeehawVideo
//
//  Created by <PERSON> on 7/21/25.
//

import Foundation
import SwiftUI

struct CompressionResult {
    let success: Bool
    let inputPath: String
    let outputPath: String?
    let error: String?
    let originalSize: Int64
    let compressedSize: Int64?
    let compressionRatio: Double?
}

@MainActor
class VideoCompressionService: ObservableObject {
    @Published var isCompressing = false
    @Published var currentFile = ""
    @Published var progress = 0.0
    @Published var compressionResults: [CompressionResult] = []
    @Published var compressionLog: [String] = []
    
    private let supportedExtensions = ["mov", "mp4", "webm", "avi", "mkv"]
    
    func compressVideo(
        inputPath: String,
        outputDirectory: String,
        deleteOriginal: Bool = false
    ) async -> CompressionResult {
        
        let inputURL = URL(fileURLWithPath: inputPath)
        let fileName = inputURL.deletingPathExtension().lastPathComponent
        let fileExtension = inputURL.pathExtension.lowercased()
        
        // Check if file is supported
        guard supportedExtensions.contains(fileExtension) else {
            return CompressionResult(
                success: false,
                inputPath: inputPath,
                outputPath: nil,
                error: "Unsupported file format: \(fileExtension)",
                originalSize: 0,
                compressedSize: nil,
                compressionRatio: nil
            )
        }
        
        // Get original file size
        let originalSize = getFileSize(at: inputPath)
        
        // Determine output format based on GPS metadata presence
        let hasGPS = await detectGPSMetadata(in: inputPath)
        let outputExtension = hasGPS ? "mov" : "mp4"
        let outputFileName = "\(fileName)_zmt.\(outputExtension)"
        let outputPath = URL(fileURLWithPath: outputDirectory).appendingPathComponent(outputFileName).path
        
        currentFile = inputURL.lastPathComponent
        addToLog("Starting compression of \(currentFile)")
        
        // Get original frame rate
        let frameRate = await getFrameRate(from: inputPath)
        
        // Compress the video
        let compressionSuccess = await compressVideoFile(
            inputPath: inputPath,
            outputPath: outputPath,
            frameRate: frameRate
        )
        
        if !compressionSuccess {
            return CompressionResult(
                success: false,
                inputPath: inputPath,
                outputPath: nil,
                error: "FFmpeg compression failed",
                originalSize: originalSize,
                compressedSize: nil,
                compressionRatio: nil
            )
        }
        
        let compressedSize = getFileSize(at: outputPath)
        let compressionRatio = originalSize > 0 ? Double(compressedSize) / Double(originalSize) : 0
        
        addToLog("Compression completed. Original: \(formatFileSize(originalSize)), Compressed: \(formatFileSize(compressedSize))")
        
        // Delete original if requested
        if deleteOriginal {
            do {
                try FileManager.default.removeItem(atPath: inputPath)
                addToLog("Original file deleted: \(inputURL.lastPathComponent)")
            } catch {
                addToLog("Warning: Could not delete original file: \(error.localizedDescription)")
            }
        }
        
        return CompressionResult(
            success: true,
            inputPath: inputPath,
            outputPath: outputPath,
            error: nil,
            originalSize: originalSize,
            compressedSize: compressedSize,
            compressionRatio: compressionRatio
        )
    }
    
    private func compressVideoFile(inputPath: String, outputPath: String, frameRate: String) async -> Bool {
        return await withCheckedContinuation { continuation in
            let process = Process()
            
            // Try to find ffmpeg in common locations
            let ffmpegPaths = [
                "/opt/homebrew/bin/ffmpeg",
                "/usr/local/bin/ffmpeg",
                "/usr/bin/ffmpeg"
            ]
            
            var ffmpegPath: String?
            for path in ffmpegPaths {
                if FileManager.default.fileExists(atPath: path) {
                    ffmpegPath = path
                    break
                }
            }
            
            guard let ffmpeg = ffmpegPath else {
                addToLog("Error: ffmpeg not found in expected locations")
                continuation.resume(returning: false)
                return
            }
            
            process.executableURL = URL(fileURLWithPath: ffmpeg)
            process.arguments = [
                "-i", inputPath,
                "-vcodec", "libx264",
                "-crf", "28",
                "-preset", "fast",
                "-pix_fmt", "yuv420p",
                "-movflags", "+faststart",
                "-r", frameRate,
                "-y", // Overwrite output file
                outputPath
            ]
            
            let pipe = Pipe()
            process.standardOutput = pipe
            process.standardError = pipe
            
            do {
                try process.run()
                process.waitUntilExit()
                
                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                if let output = String(data: data, encoding: .utf8), !output.isEmpty {
                    addToLog("FFmpeg output: \(output)")
                }
                
                continuation.resume(returning: process.terminationStatus == 0)
            } catch {
                addToLog("Error running ffmpeg: \(error.localizedDescription)")
                continuation.resume(returning: false)
            }
        }
    }
    
    private func detectGPSMetadata(in filePath: String) async -> Bool {
        return await withCheckedContinuation { continuation in
            let process = Process()
            
            // Try to find ffprobe in common locations
            let ffprobePaths = [
                "/opt/homebrew/bin/ffprobe",
                "/usr/local/bin/ffprobe",
                "/usr/bin/ffprobe"
            ]
            
            var ffprobePath: String?
            for path in ffprobePaths {
                if FileManager.default.fileExists(atPath: path) {
                    ffprobePath = path
                    break
                }
            }
            
            guard let ffprobe = ffprobePath else {
                continuation.resume(returning: false)
                return
            }
            
            process.executableURL = URL(fileURLWithPath: ffprobe)
            process.arguments = [
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                filePath
            ]
            
            let pipe = Pipe()
            process.standardOutput = pipe
            process.standardError = pipe
            
            do {
                try process.run()
                process.waitUntilExit()
                
                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                if let output = String(data: data, encoding: .utf8) {
                    let hasGPS = output.contains("com.apple.quicktime.location.ISO6709") ||
                                output.contains("location.latitude") ||
                                output.contains("location.longitude")
                    continuation.resume(returning: hasGPS)
                } else {
                    continuation.resume(returning: false)
                }
            } catch {
                continuation.resume(returning: false)
            }
        }
    }
    
    private func getFrameRate(from filePath: String) async -> String {
        return await withCheckedContinuation { continuation in
            let process = Process()
            
            let ffprobePaths = [
                "/opt/homebrew/bin/ffprobe",
                "/usr/local/bin/ffprobe",
                "/usr/bin/ffprobe"
            ]
            
            var ffprobePath: String?
            for path in ffprobePaths {
                if FileManager.default.fileExists(atPath: path) {
                    ffprobePath = path
                    break
                }
            }
            
            guard let ffprobe = ffprobePath else {
                continuation.resume(returning: "30") // Default fallback
                return
            }
            
            process.executableURL = URL(fileURLWithPath: ffprobe)
            process.arguments = [
                "-v", "quiet",
                "-select_streams", "v:0",
                "-show_entries", "stream=r_frame_rate",
                "-of", "csv=p=0",
                filePath
            ]
            
            let pipe = Pipe()
            process.standardOutput = pipe
            
            do {
                try process.run()
                process.waitUntilExit()
                
                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                if let output = String(data: data, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines),
                   !output.isEmpty {
                    // Parse frame rate (e.g., "30000/1001" -> "29.97")
                    if output.contains("/") {
                        let components = output.split(separator: "/")
                        if components.count == 2,
                           let numerator = Double(components[0]),
                           let denominator = Double(components[1]),
                           denominator != 0 {
                            let frameRate = numerator / denominator
                            continuation.resume(returning: String(format: "%.2f", frameRate))
                        } else {
                            continuation.resume(returning: "30")
                        }
                    } else {
                        continuation.resume(returning: output)
                    }
                } else {
                    continuation.resume(returning: "30")
                }
            } catch {
                continuation.resume(returning: "30")
            }
        }
    }
    
    private func getFileSize(at path: String) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
    
    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private func addToLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        compressionLog.append("[\(timestamp)] \(message)")
    }
}
