//
//  ResultsView.swift
//  YeehawVideo
//
//  Created by <PERSON> on 7/21/25.
//

import SwiftUI

struct ResultsView: View {
    let results: [CompressionResult]
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                if results.isEmpty {
                    ContentUnavailableView(
                        "No Results",
                        systemImage: "tray",
                        description: Text("No compression results to display")
                    )
                } else {
                    List {
                        summarySection
                        resultsSection
                    }
                }
            }
            .navigationTitle("Compression Results")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var summarySection: some View {
        Section("Summary") {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Total Files:")
                    Spacer()
                    Text("\(results.count)")
                        .fontWeight(.semibold)
                }
                
                HStack {
                    Text("Successful:")
                    Spacer()
                    Text("\(successfulCount)")
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
                
                HStack {
                    Text("Failed:")
                    Spacer()
                    Text("\(failedCount)")
                        .fontWeight(.semibold)
                        .foregroundColor(.red)
                }
                
                if totalOriginalSize > 0 && totalCompressedSize > 0 {
                    Divider()
                    
                    HStack {
                        Text("Original Size:")
                        Spacer()
                        Text(formatFileSize(totalOriginalSize))
                            .fontWeight(.semibold)
                    }
                    
                    HStack {
                        Text("Compressed Size:")
                        Spacer()
                        Text(formatFileSize(totalCompressedSize))
                            .fontWeight(.semibold)
                    }
                    
                    HStack {
                        Text("Space Saved:")
                        Spacer()
                        Text(formatFileSize(totalOriginalSize - totalCompressedSize))
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                    
                    HStack {
                        Text("Compression Ratio:")
                        Spacer()
                        Text(String(format: "%.1f%%", (1.0 - Double(totalCompressedSize) / Double(totalOriginalSize)) * 100))
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                    }
                }
            }
            .padding(.vertical, 4)
        }
    }
    
    private var resultsSection: some View {
        Section("Files") {
            ForEach(Array(results.enumerated()), id: \.offset) { index, result in
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: result.success ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(result.success ? .green : .red)
                        
                        VStack(alignment: .leading) {
                            Text(URL(fileURLWithPath: result.inputPath).lastPathComponent)
                                .fontWeight(.medium)
                            
                            if let outputPath = result.outputPath {
                                Text("→ \(URL(fileURLWithPath: outputPath).lastPathComponent)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                        
                        if result.success, let compressedSize = result.compressedSize {
                            VStack(alignment: .trailing) {
                                Text(formatFileSize(compressedSize))
                                    .font(.caption)
                                    .fontWeight(.medium)
                                
                                if let ratio = result.compressionRatio {
                                    Text(String(format: "%.1f%%", (1.0 - ratio) * 100))
                                        .font(.caption2)
                                        .foregroundColor(.green)
                                }
                            }
                        }
                    }
                    
                    if let error = result.error {
                        Text(error)
                            .font(.caption)
                            .foregroundColor(.red)
                            .padding(.leading, 24)
                    }
                    
                    if result.success && result.originalSize > 0 {
                        HStack {
                            Text("Original: \(formatFileSize(result.originalSize))")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                            
                            if let compressedSize = result.compressedSize {
                                Text("• Compressed: \(formatFileSize(compressedSize))")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                
                                Text("• Saved: \(formatFileSize(result.originalSize - compressedSize))")
                                    .font(.caption2)
                                    .foregroundColor(.green)
                            }
                        }
                        .padding(.leading, 24)
                    }
                }
                .padding(.vertical, 2)
            }
        }
    }
    
    private var successfulCount: Int {
        results.filter { $0.success }.count
    }
    
    private var failedCount: Int {
        results.filter { !$0.success }.count
    }
    
    private var totalOriginalSize: Int64 {
        results.reduce(0) { $0 + $1.originalSize }
    }
    
    private var totalCompressedSize: Int64 {
        results.compactMap { $0.compressedSize }.reduce(0, +)
    }
    
    private func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
}

#Preview {
    ResultsView(results: [
        CompressionResult(
            success: true,
            inputPath: "/path/to/video1.mov",
            outputPath: "/path/to/video1_zmt.mov",
            error: nil,
            originalSize: 1024 * 1024 * 100,
            compressedSize: 1024 * 1024 * 30,
            compressionRatio: 0.3
        ),
        CompressionResult(
            success: false,
            inputPath: "/path/to/video2.mp4",
            outputPath: nil,
            error: "Unsupported format",
            originalSize: 1024 * 1024 * 50,
            compressedSize: nil,
            compressionRatio: nil
        )
    ])
}
