//
//  GPSMetadataService.swift
//  YeehawVideo
//
//  Created by <PERSON> on 7/21/25.
//

import Foundation
import SwiftUI

struct GPSData: Codable {
    let latitude: Double
    let longitude: Double
    let iso6709: String?
    let timestamp: String?
    
    var latitudeRef: String {
        return latitude >= 0 ? "N" : "S"
    }
    
    var longitudeRef: String {
        return longitude >= 0 ? "E" : "W"
    }
    
    var absoluteLatitude: Double {
        return abs(latitude)
    }
    
    var absoluteLongitude: Double {
        return abs(longitude)
    }
}

@MainActor
class GPSMetadataService: ObservableObject {
    @Published var isProcessing = false
    @Published var gpsLog: [String] = []
    
    func extractGPSMetadata(from filePath: String) async -> GPSData? {
        addToLog("Extracting GPS metadata from \(URL(fileURLWithPath: filePath).lastPathComponent)")
        
        return await withCheckedContinuation { continuation in
            let process = Process()
            
            // Try to find ffprobe in common locations
            let ffprobePaths = [
                "/opt/homebrew/bin/ffprobe",
                "/usr/local/bin/ffprobe",
                "/usr/bin/ffprobe"
            ]
            
            var ffprobePath: String?
            for path in ffprobePaths {
                if FileManager.default.fileExists(atPath: path) {
                    ffprobePath = path
                    break
                }
            }
            
            guard let ffprobe = ffprobePath else {
                addToLog("Error: ffprobe not found")
                continuation.resume(returning: nil)
                return
            }
            
            process.executableURL = URL(fileURLWithPath: ffprobe)
            process.arguments = [
                "-v", "quiet",
                "-print_format", "json",
                "-show_format",
                filePath
            ]
            
            let pipe = Pipe()
            process.standardOutput = pipe
            process.standardError = pipe
            
            do {
                try process.run()
                process.waitUntilExit()
                
                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                if let output = String(data: data, encoding: .utf8) {
                    let gpsData = parseGPSFromFFProbeOutput(output)
                    if gpsData != nil {
                        addToLog("GPS metadata found: lat=\(gpsData!.latitude), lon=\(gpsData!.longitude)")
                    } else {
                        addToLog("No GPS metadata found")
                    }
                    continuation.resume(returning: gpsData)
                } else {
                    addToLog("No output from ffprobe")
                    continuation.resume(returning: nil)
                }
            } catch {
                addToLog("Error running ffprobe: \(error.localizedDescription)")
                continuation.resume(returning: nil)
            }
        }
    }
    
    func saveGPSToJSON(_ gpsData: GPSData, for filePath: String, in outputDirectory: String) async -> Bool {
        let inputURL = URL(fileURLWithPath: filePath)
        let fileName = inputURL.deletingPathExtension().lastPathComponent
        let jsonFileName = "\(fileName)_gps.json"
        let jsonPath = URL(fileURLWithPath: outputDirectory).appendingPathComponent(jsonFileName).path
        
        do {
            let encoder = JSONEncoder()
            encoder.outputFormatting = .prettyPrinted
            let jsonData = try encoder.encode(gpsData)
            
            try jsonData.write(to: URL(fileURLWithPath: jsonPath))
            addToLog("GPS data saved to \(jsonFileName)")
            return true
        } catch {
            addToLog("Error saving GPS JSON: \(error.localizedDescription)")
            return false
        }
    }
    
    func injectGPSMetadata(_ gpsData: GPSData, into filePath: String) async -> Bool {
        // Only inject into .mov files
        guard filePath.lowercased().hasSuffix(".mov") else {
            addToLog("Skipping GPS injection for non-MOV file")
            return true
        }
        
        addToLog("Injecting GPS metadata into \(URL(fileURLWithPath: filePath).lastPathComponent)")
        
        return await withCheckedContinuation { continuation in
            let process = Process()
            
            // Try to find exiftool in common locations
            let exiftoolPaths = [
                "/opt/homebrew/bin/exiftool",
                "/usr/local/bin/exiftool",
                "/usr/bin/exiftool"
            ]
            
            var exiftoolPath: String?
            for path in exiftoolPaths {
                if FileManager.default.fileExists(atPath: path) {
                    exiftoolPath = path
                    break
                }
            }
            
            guard let exiftool = exiftoolPath else {
                addToLog("Warning: exiftool not found, skipping GPS injection")
                continuation.resume(returning: true) // Not a failure, just skip
                return
            }
            
            process.executableURL = URL(fileURLWithPath: exiftool)
            
            var arguments = [
                "-overwrite_original",
                "-GPSLatitude=\(gpsData.absoluteLatitude)",
                "-GPSLongitude=\(gpsData.absoluteLongitude)",
                "-GPSLatitudeRef=\(gpsData.latitudeRef)",
                "-GPSLongitudeRef=\(gpsData.longitudeRef)"
            ]
            
            // Add ISO6709 if available
            if let iso6709 = gpsData.iso6709 {
                arguments.append("-com.apple.quicktime.location.ISO6709=\(iso6709)")
            }
            
            arguments.append(filePath)
            process.arguments = arguments
            
            let pipe = Pipe()
            process.standardOutput = pipe
            process.standardError = pipe
            
            do {
                try process.run()
                process.waitUntilExit()
                
                let data = pipe.fileHandleForReading.readDataToEndOfFile()
                if let output = String(data: data, encoding: .utf8), !output.isEmpty {
                    addToLog("Exiftool output: \(output)")
                }
                
                let success = process.terminationStatus == 0
                if success {
                    addToLog("GPS metadata successfully injected")
                } else {
                    addToLog("GPS injection failed with exit code \(process.terminationStatus)")
                }
                
                continuation.resume(returning: success)
            } catch {
                addToLog("Error running exiftool: \(error.localizedDescription)")
                continuation.resume(returning: false)
            }
        }
    }
    
    private func parseGPSFromFFProbeOutput(_ output: String) -> GPSData? {
        guard let data = output.data(using: .utf8) else { return nil }
        
        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let format = json["format"] as? [String: Any],
               let tags = format["tags"] as? [String: Any] {
                
                // Look for different GPS metadata formats
                var latitude: Double?
                var longitude: Double?
                var iso6709: String?
                
                // Check for com.apple.quicktime.location.ISO6709
                if let iso6709String = tags["com.apple.quicktime.location.ISO6709"] as? String {
                    iso6709 = iso6709String
                    let coords = parseISO6709(iso6709String)
                    latitude = coords.latitude
                    longitude = coords.longitude
                }
                
                // Check for individual latitude/longitude fields
                if latitude == nil || longitude == nil {
                    if let latString = tags["location.latitude"] as? String,
                       let lonString = tags["location.longitude"] as? String {
                        latitude = Double(latString)
                        longitude = Double(lonString)
                    }
                }
                
                // Check for other common GPS tag formats
                if latitude == nil || longitude == nil {
                    for (key, value) in tags {
                        let keyLower = key.lowercased()
                        if keyLower.contains("latitude") || keyLower.contains("lat") {
                            if let valueString = value as? String {
                                latitude = Double(valueString)
                            }
                        }
                        if keyLower.contains("longitude") || keyLower.contains("lon") {
                            if let valueString = value as? String {
                                longitude = Double(valueString)
                            }
                        }
                    }
                }
                
                if let lat = latitude, let lon = longitude {
                    return GPSData(
                        latitude: lat,
                        longitude: lon,
                        iso6709: iso6709,
                        timestamp: ISO8601DateFormatter().string(from: Date())
                    )
                }
            }
        } catch {
            addToLog("Error parsing GPS metadata: \(error.localizedDescription)")
        }
        
        return nil
    }
    
    private func parseISO6709(_ iso6709: String) -> (latitude: Double?, longitude: Double?) {
        // Parse ISO 6709 format like "+37.7749-122.4194/"
        let pattern = #"([+-]\d+\.?\d*)([+-]\d+\.?\d*)"#
        
        do {
            let regex = try NSRegularExpression(pattern: pattern)
            let range = NSRange(location: 0, length: iso6709.utf16.count)
            
            if let match = regex.firstMatch(in: iso6709, range: range) {
                let latRange = Range(match.range(at: 1), in: iso6709)
                let lonRange = Range(match.range(at: 2), in: iso6709)
                
                if let latRange = latRange, let lonRange = lonRange {
                    let latString = String(iso6709[latRange])
                    let lonString = String(iso6709[lonRange])
                    
                    return (Double(latString), Double(lonString))
                }
            }
        } catch {
            addToLog("Error parsing ISO6709 format: \(error.localizedDescription)")
        }
        
        return (nil, nil)
    }
    
    private func addToLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        gpsLog.append("[\(timestamp)] \(message)")
    }
}
