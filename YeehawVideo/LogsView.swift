//
//  LogsView.swift
//  YeehawVideo
//
//  Created by <PERSON> on 7/21/25.
//

import SwiftUI

struct LogsView: View {
    let compressionLogs: [String]
    let gpsLogs: [String]
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab = 0
    
    var body: some View {
        NavigationView {
            VStack {
                Picker("Log Type", selection: $selectedTab) {
                    Text("Compression").tag(0)
                    Text("GPS Metadata").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                TabView(selection: $selectedTab) {
                    logListView(logs: compressionLogs, emptyMessage: "No compression logs")
                        .tag(0)

                    logListView(logs: gpsLogs, emptyMessage: "No GPS metadata logs")
                        .tag(1)
                }
            }
            .navigationTitle("Logs")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button("Done") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .secondaryAction) {
                    But<PERSON>("Export") {
                        exportLogs()
                    }
                    .disabled(compressionLogs.isEmpty && gpsLogs.isEmpty)
                }
            }
        }
    }
    
    private func logListView(logs: [String], emptyMessage: String) -> some View {
        Group {
            if logs.isEmpty {
                ContentUnavailableView(
                    emptyMessage,
                    systemImage: "doc.text",
                    description: Text("No log entries to display")
                )
            } else {
                List {
                    ForEach(Array(logs.enumerated()), id: \.offset) { index, log in
                        VStack(alignment: .leading, spacing: 4) {
                            Text(log)
                                .font(.system(.caption, design: .monospaced))
                                .textSelection(.enabled)
                        }
                        .padding(.vertical, 2)
                    }
                }
                .listStyle(PlainListStyle())
            }
        }
    }
    
    private func exportLogs() {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        let timestamp = dateFormatter.string(from: Date())
        
        var logContent = "YeehawVideo Logs - \(timestamp)\n"
        logContent += "=" + String(repeating: "=", count: 50) + "\n\n"
        
        if !compressionLogs.isEmpty {
            logContent += "COMPRESSION LOGS:\n"
            logContent += "-" + String(repeating: "-", count: 30) + "\n"
            for log in compressionLogs {
                logContent += log + "\n"
            }
            logContent += "\n"
        }
        
        if !gpsLogs.isEmpty {
            logContent += "GPS METADATA LOGS:\n"
            logContent += "-" + String(repeating: "-", count: 30) + "\n"
            for log in gpsLogs {
                logContent += log + "\n"
            }
            logContent += "\n"
        }
        
        // Save to desktop
        let desktopURL = FileManager.default.urls(for: .desktopDirectory, in: .userDomainMask).first
        if let desktopURL = desktopURL {
            let logFileURL = desktopURL.appendingPathComponent("YeehawVideo_Logs_\(timestamp).txt")
            
            do {
                try logContent.write(to: logFileURL, atomically: true, encoding: .utf8)
                print("Logs exported to: \(logFileURL.path)")
            } catch {
                print("Failed to export logs: \(error)")
            }
        }
    }
}

#Preview {
    LogsView(
        compressionLogs: [
            "[10:30:15] Starting compression of video1.mov",
            "[10:30:16] FFmpeg output: frame=  120 fps= 30 q=28.0 size=    1024kB time=00:00:04.00 bitrate=2097.2kbits/s speed=   1x",
            "[10:30:20] Compression completed. Original: 100 MB, Compressed: 30 MB",
            "[10:30:20] Original file deleted: video1.mov"
        ],
        gpsLogs: [
            "[10:30:15] Extracting GPS metadata from video1.mov",
            "[10:30:15] GPS metadata found: lat=37.7749, lon=-122.4194",
            "[10:30:20] GPS data saved to video1_gps.json",
            "[10:30:21] Injecting GPS metadata into video1_zmt.mov",
            "[10:30:21] GPS metadata successfully injected"
        ]
    )
}
