//
//  DependencyManager.swift
//  YeehawVideo
//
//  Created by <PERSON> on 7/21/25.
//

import Foundation
import SwiftUI

@MainActor
class DependencyManager: ObservableObject {
    @Published var isCheckingDependencies = false
    @Published var dependencyStatus: [String: Bool] = [:]
    @Published var installationProgress: String = ""
    @Published var showInstallationAlert = false
    @Published var installationError: String?
    
    private let requiredTools = ["brew", "ffmpeg", "jq", "exiftool"]
    
    init() {
        // Initialize all tools as not installed
        for tool in requiredTools {
            dependencyStatus[tool] = false
        }
    }
    
    func checkAllDependencies() async {
        isCheckingDependencies = true
        installationProgress = "Checking dependencies..."
        
        for tool in requiredTools {
            dependencyStatus[tool] = await checkToolInstalled(tool)
        }
        
        isCheckingDependencies = false
        
        // If any tools are missing, show installation prompt
        if dependencyStatus.values.contains(false) {
            showInstallationAlert = true
        }
    }
    
    private func checkToolInstalled(_ tool: String) async -> Bool {
        // First try using 'which' command with proper PATH
        let whichResult = await checkWithWhich(tool)
        if whichResult {
            return true
        }

        // If 'which' fails, check common installation paths directly
        return await checkCommonPaths(tool)
    }

    private func checkWithWhich(_ tool: String) async -> Bool {
        return await withCheckedContinuation { continuation in
            let process = Process()
            process.executableURL = URL(fileURLWithPath: "/usr/bin/which")
            process.arguments = [tool]

            // Set up proper PATH environment that includes Homebrew paths
            var environment = ProcessInfo.processInfo.environment
            let homebrewPaths = "/opt/homebrew/bin:/usr/local/bin"
            if let existingPath = environment["PATH"] {
                environment["PATH"] = "\(homebrewPaths):\(existingPath)"
            } else {
                environment["PATH"] = "\(homebrewPaths):/usr/bin:/bin"
            }
            process.environment = environment

            let pipe = Pipe()
            process.standardOutput = pipe
            process.standardError = pipe

            do {
                try process.run()
                process.waitUntilExit()
                continuation.resume(returning: process.terminationStatus == 0)
            } catch {
                continuation.resume(returning: false)
            }
        }
    }

    private func checkCommonPaths(_ tool: String) async -> Bool {
        let commonPaths: [String]

        switch tool {
        case "brew":
            commonPaths = [
                "/opt/homebrew/bin/brew",
                "/usr/local/bin/brew"
            ]
        case "ffmpeg":
            commonPaths = [
                "/opt/homebrew/bin/ffmpeg",
                "/usr/local/bin/ffmpeg",
                "/usr/bin/ffmpeg"
            ]
        case "jq":
            commonPaths = [
                "/opt/homebrew/bin/jq",
                "/usr/local/bin/jq",
                "/usr/bin/jq"
            ]
        case "exiftool":
            commonPaths = [
                "/opt/homebrew/bin/exiftool",
                "/usr/local/bin/exiftool",
                "/usr/bin/exiftool"
            ]
        default:
            return false
        }

        for path in commonPaths {
            if FileManager.default.fileExists(atPath: path) {
                // Double-check by trying to execute it with --version or --help
                let works = await testExecutable(at: path, tool: tool)
                if works {
                    return true
                }
            }
        }

        return false
    }

    private func testExecutable(at path: String, tool: String) async -> Bool {
        return await withCheckedContinuation { continuation in
            let process = Process()
            process.executableURL = URL(fileURLWithPath: path)

            // Use appropriate test argument for each tool
            switch tool {
            case "brew":
                process.arguments = ["--version"]
            case "ffmpeg":
                process.arguments = ["-version"]
            case "jq":
                process.arguments = ["--version"]
            case "exiftool":
                process.arguments = ["-ver"]
            default:
                process.arguments = ["--version"]
            }

            let pipe = Pipe()
            process.standardOutput = pipe
            process.standardError = pipe

            do {
                try process.run()
                process.waitUntilExit()
                continuation.resume(returning: process.terminationStatus == 0)
            } catch {
                continuation.resume(returning: false)
            }
        }
    }
    
    func installMissingDependencies() async {
        installationProgress = "Installing dependencies..."
        installationError = nil
        
        // First, check if Homebrew is installed
        if !dependencyStatus["brew", default: false] {
            installationProgress = "Installing Homebrew..."
            let brewInstalled = await installHomebrew()
            dependencyStatus["brew"] = brewInstalled
            
            if !brewInstalled {
                installationError = "Failed to install Homebrew. Please install manually."
                return
            }
        }
        
        // Install other tools via Homebrew
        let toolsToInstall = requiredTools.filter { tool in
            tool != "brew" && !dependencyStatus[tool, default: false]
        }
        
        for tool in toolsToInstall {
            installationProgress = "Installing \(tool)..."
            let installed = await installViaBrew(tool)
            dependencyStatus[tool] = installed
            
            if !installed {
                installationError = "Failed to install \(tool). Please check your Homebrew installation."
                return
            }
        }
        
        installationProgress = "All dependencies installed successfully!"
    }
    
    private func installHomebrew() async -> Bool {
        return await withCheckedContinuation { continuation in
            let process = Process()
            process.executableURL = URL(fileURLWithPath: "/bin/bash")
            process.arguments = ["-c", "/bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""]
            
            let pipe = Pipe()
            process.standardOutput = pipe
            process.standardError = pipe
            
            do {
                try process.run()
                process.waitUntilExit()
                continuation.resume(returning: process.terminationStatus == 0)
            } catch {
                continuation.resume(returning: false)
            }
        }
    }
    
    private func installViaBrew(_ tool: String) async -> Bool {
        return await withCheckedContinuation { continuation in
            let process = Process()
            process.executableURL = URL(fileURLWithPath: "/opt/homebrew/bin/brew")
            process.arguments = ["install", tool]
            
            let pipe = Pipe()
            process.standardOutput = pipe
            process.standardError = pipe
            
            do {
                try process.run()
                process.waitUntilExit()
                continuation.resume(returning: process.terminationStatus == 0)
            } catch {
                // Try Intel Homebrew path
                process.executableURL = URL(fileURLWithPath: "/usr/local/bin/brew")
                do {
                    try process.run()
                    process.waitUntilExit()
                    continuation.resume(returning: process.terminationStatus == 0)
                } catch {
                    continuation.resume(returning: false)
                }
            }
        }
    }
    
    var allDependenciesInstalled: Bool {
        return dependencyStatus.values.allSatisfy { $0 }
    }
    
    var missingDependencies: [String] {
        return requiredTools.filter { !dependencyStatus[$0, default: false] }
    }
}
