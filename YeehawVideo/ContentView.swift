//
//  ContentView.swift
//  YeehawVideo
//
//  Created by <PERSON> on 7/21/25.
//

import SwiftUI
import UniformTypeIdentifiers

struct ContentView: View {
    @StateObject private var dependencyManager = DependencyManager()
    @StateObject private var compressionService = VideoCompressionService()
    @StateObject private var gpsService = GPSMetadataService()
    @EnvironmentObject private var errorHandler: ErrorHandler

    @State private var selectedInputPath: String = ""
    @State private var selectedOutputPath: String = ""
    @State private var deleteOriginal = false
    @State private var isProcessingBatch = false
    @State private var showingFilePicker = false
    @State private var showingFolderPicker = false
    @State private var showingOutputPicker = false
    @State private var showingResults = false
    @State private var showingLogs = false

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                headerView

                if !dependencyManager.allDependenciesInstalled {
                    dependencyStatusView
                } else {
                    mainContentView
                }

                Spacer()
            }
            .padding()
            .navigationTitle("YeehawVideo")
            .onAppear {
                Task {
                    await dependencyManager.checkAllDependencies()
                }
            }
            .alert("Install Dependencies", isPresented: $dependencyManager.showInstallationAlert) {
                Button("Install") {
                    Task {
                        await dependencyManager.installMissingDependencies()
                    }
                }
                Button("Cancel", role: .cancel) { }
            } message: {
                Text("This app requires Homebrew, ffmpeg, jq, and exiftool to function. Would you like to install the missing dependencies?")
            }
            .sheet(isPresented: $showingResults) {
                ResultsView(results: compressionService.compressionResults)
            }
            .sheet(isPresented: $showingLogs) {
                LogsView(
                    compressionLogs: compressionService.compressionLog,
                    gpsLogs: gpsService.gpsLog
                )
            }
        }
    }

    private var headerView: some View {
        VStack {
            Image(systemName: "video.badge.gearshape")
                .font(.system(size: 60))
                .foregroundColor(.blue)

            Text("GPS-Aware Video Compressor")
                .font(.title2)
                .fontWeight(.semibold)

            Text("Compress videos while preserving GPS metadata")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }

    private var dependencyStatusView: some View {
        VStack(spacing: 15) {
            Text("Checking Dependencies...")
                .font(.headline)

            if dependencyManager.isCheckingDependencies {
                ProgressView()
                    .scaleEffect(1.2)
            } else {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(["brew", "ffmpeg", "jq", "exiftool"], id: \.self) { tool in
                        HStack {
                            Image(systemName: dependencyManager.dependencyStatus[tool, default: false] ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(dependencyManager.dependencyStatus[tool, default: false] ? .green : .red)
                            Text(tool)
                            Spacer()
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(8)

                HStack {
                    Button("Refresh") {
                        Task {
                            await dependencyManager.checkAllDependencies()
                        }
                    }
                    .buttonStyle(.bordered)

                    if !dependencyManager.allDependenciesInstalled {
                        Button("Install Missing") {
                            Task {
                                await dependencyManager.installMissingDependencies()
                                // Re-check after installation
                                await dependencyManager.checkAllDependencies()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }

                if !dependencyManager.installationProgress.isEmpty {
                    Text(dependencyManager.installationProgress)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                if let error = dependencyManager.installationError {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                }
            }
        }
    }

    private var mainContentView: some View {
        VStack(spacing: 20) {
            inputSelectionView
            outputSelectionView
            optionsView
            actionButtonsView

            if isProcessingBatch || compressionService.isCompressing {
                progressView
            }
        }
    }

    private var inputSelectionView: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Input")
                .font(.headline)

            HStack {
                VStack(alignment: .leading) {
                    if selectedInputPath.isEmpty {
                        Text("No file or folder selected")
                            .foregroundColor(.secondary)
                    } else {
                        Text(URL(fileURLWithPath: selectedInputPath).lastPathComponent)
                            .fontWeight(.medium)
                        Text(selectedInputPath)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                HStack {
                    Button("Select File") {
                        showingFilePicker = true
                    }
                    .fileImporter(
                        isPresented: $showingFilePicker,
                        allowedContentTypes: [.movie, .video],
                        allowsMultipleSelection: false
                    ) { result in
                        switch result {
                        case .success(let urls):
                            if let url = urls.first {
                                selectedInputPath = url.path
                            }
                        case .failure(let error):
                            print("File selection failed: \(error)")
                        }
                    }

                    Button("Select Folder") {
                        showingFolderPicker = true
                    }
                    .fileImporter(
                        isPresented: $showingFolderPicker,
                        allowedContentTypes: [.folder],
                        allowsMultipleSelection: false
                    ) { result in
                        switch result {
                        case .success(let urls):
                            if let url = urls.first {
                                selectedInputPath = url.path
                            }
                        case .failure(let error):
                            print("Folder selection failed: \(error)")
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }

    private var outputSelectionView: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Output Directory")
                .font(.headline)

            HStack {
                VStack(alignment: .leading) {
                    if selectedOutputPath.isEmpty {
                        Text("No output directory selected")
                            .foregroundColor(.secondary)
                    } else {
                        Text(URL(fileURLWithPath: selectedOutputPath).lastPathComponent)
                            .fontWeight(.medium)
                        Text(selectedOutputPath)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                Button("Select Directory") {
                    showingOutputPicker = true
                }
                .fileImporter(
                    isPresented: $showingOutputPicker,
                    allowedContentTypes: [.folder],
                    allowsMultipleSelection: false
                ) { result in
                    switch result {
                    case .success(let urls):
                        if let url = urls.first {
                            selectedOutputPath = url.path
                        }
                    case .failure(let error):
                        print("Output directory selection failed: \(error)")
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }

    private var optionsView: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("Options")
                .font(.headline)

            Toggle("Delete original files after compression", isOn: $deleteOriginal)
                .toggleStyle(SwitchToggleStyle())
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }

    private var actionButtonsView: some View {
        HStack(spacing: 15) {
            Button("Compress") {
                Task {
                    await startCompression()
                }
            }
            .buttonStyle(.borderedProminent)
            .disabled(selectedInputPath.isEmpty || selectedOutputPath.isEmpty || isProcessingBatch)

            Button("View Results") {
                showingResults = true
            }
            .buttonStyle(.bordered)
            .disabled(compressionService.compressionResults.isEmpty)

            Button("View Logs") {
                showingLogs = true
            }
            .buttonStyle(.bordered)
        }
    }

    private var progressView: some View {
        VStack(spacing: 10) {
            if !compressionService.currentFile.isEmpty {
                Text("Processing: \(compressionService.currentFile)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            ProgressView(value: compressionService.progress)
                .progressViewStyle(LinearProgressViewStyle())
        }
        .padding()
        .background(Color.blue.opacity(0.1))
        .cornerRadius(8)
    }

    private func startCompression() async {
        // Validate inputs before starting
        if let inputError = errorHandler.validateInputPath(selectedInputPath) {
            errorHandler.handle(inputError, context: "Input validation")
            return
        }

        if let outputError = errorHandler.validateOutputPath(selectedOutputPath) {
            errorHandler.handle(outputError, context: "Output validation")
            return
        }

        // Check dependencies
        let dependencyErrors = await errorHandler.validateDependencies()
        if !dependencyErrors.isEmpty {
            for error in dependencyErrors {
                errorHandler.handle(error, context: "Dependency check", severity: .warning)
            }
        }

        isProcessingBatch = true
        compressionService.compressionResults.removeAll()
        compressionService.compressionLog.removeAll()
        gpsService.gpsLog.removeAll()
        errorHandler.clearErrors()

        let inputURL = URL(fileURLWithPath: selectedInputPath)
        var filesToProcess: [String] = []

        // Determine if input is a file or directory
        var isDirectory: ObjCBool = false
        if FileManager.default.fileExists(atPath: selectedInputPath, isDirectory: &isDirectory) {
            if isDirectory.boolValue {
                // Process all video files in directory
                filesToProcess = getVideoFiles(in: selectedInputPath)
            } else {
                // Process single file
                filesToProcess = [selectedInputPath]
            }
        }

        // Check disk space
        let estimatedSize = filesToProcess.reduce(0) { total, filePath in
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: filePath)
                return total + Int(attributes[.size] as? Int64 ?? 0)
            } catch {
                return total
            }
        }

        if let spaceError = errorHandler.checkDiskSpace(outputPath: selectedOutputPath, estimatedSize: Int64(estimatedSize / 3)) {
            errorHandler.handle(spaceError, context: "Disk space check", severity: .warning)
        }

        for (index, filePath) in filesToProcess.enumerated() {
            compressionService.progress = Double(index) / Double(filesToProcess.count)

            // Extract GPS metadata first
            if let gpsData = await gpsService.extractGPSMetadata(from: filePath) {
                _ = await gpsService.saveGPSToJSON(gpsData, for: filePath, in: selectedOutputPath)
            }

            // Compress the video
            let result = await compressionService.compressVideo(
                inputPath: filePath,
                outputDirectory: selectedOutputPath,
                deleteOriginal: deleteOriginal
            )

            compressionService.compressionResults.append(result)

            // Log any compression failures
            if !result.success, let error = result.error {
                errorHandler.handle(
                    .compressionFailed(error),
                    context: "File: \(URL(fileURLWithPath: filePath).lastPathComponent)",
                    severity: .warning
                )
            }

            // Inject GPS metadata if compression was successful and GPS data exists
            if result.success, let outputPath = result.outputPath,
               let gpsData = await gpsService.extractGPSMetadata(from: filePath) {
                let injectionSuccess = await gpsService.injectGPSMetadata(gpsData, into: outputPath)

                if !injectionSuccess {
                    errorHandler.handle(
                        .gpsInjectionFailed("Failed to inject GPS metadata"),
                        context: "File: \(URL(fileURLWithPath: outputPath).lastPathComponent)",
                        severity: .warning
                    )
                }
            }
        }

        compressionService.progress = 1.0
        isProcessingBatch = false
        showingResults = true
    }

    private func getVideoFiles(in directoryPath: String) -> [String] {
        let supportedExtensions = ["mov", "mp4", "webm", "avi", "mkv"]
        var videoFiles: [String] = []

        do {
            let contents = try FileManager.default.contentsOfDirectory(atPath: directoryPath)
            for item in contents {
                let itemPath = URL(fileURLWithPath: directoryPath).appendingPathComponent(item).path
                let fileExtension = URL(fileURLWithPath: itemPath).pathExtension.lowercased()

                if supportedExtensions.contains(fileExtension) {
                    videoFiles.append(itemPath)
                }
            }
        } catch {
            print("Error reading directory: \(error)")
        }

        return videoFiles.sorted()
    }
}

#Preview {
    ContentView()
}
