//
//  ErrorHandler.swift
//  YeehawVideo
//
//  Created by <PERSON> on 7/21/25.
//

import Foundation
import SwiftUI

enum YeehawVideoError: LocalizedError {
    case dependencyNotFound(String)
    case invalidInputPath
    case invalidOutputPath
    case insufficientDiskSpace(required: Int64, available: Int64)
    case unsupportedFileFormat(String)
    case compressionFailed(String)
    case gpsExtractionFailed(String)
    case gpsInjectionFailed(String)
    case fileSystemError(String)
    case processExecutionError(String)
    
    var errorDescription: String? {
        switch self {
        case .dependencyNotFound(let tool):
            return "Required tool '\(tool)' is not installed or not found in PATH"
        case .invalidInputPath:
            return "The selected input file or folder is invalid or does not exist"
        case .invalidOutputPath:
            return "The selected output directory is invalid or not writable"
        case .insufficientDiskSpace(let required, let available):
            return "Insufficient disk space. Required: \(ByteCountFormatter().string(fromByteCount: required)), Available: \(ByteCountFormatter().string(fromByteCount: available))"
        case .unsupportedFileFormat(let format):
            return "Unsupported file format: \(format). Supported formats: .mov, .mp4, .webm, .avi, .mkv"
        case .compressionFailed(let reason):
            return "Video compression failed: \(reason)"
        case .gpsExtractionFailed(let reason):
            return "GPS metadata extraction failed: \(reason)"
        case .gpsInjectionFailed(let reason):
            return "GPS metadata injection failed: \(reason)"
        case .fileSystemError(let reason):
            return "File system error: \(reason)"
        case .processExecutionError(let reason):
            return "Process execution error: \(reason)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .dependencyNotFound(let tool):
            return "Install \(tool) using Homebrew: brew install \(tool)"
        case .invalidInputPath:
            return "Please select a valid video file or folder containing video files"
        case .invalidOutputPath:
            return "Please select a valid, writable output directory"
        case .insufficientDiskSpace:
            return "Free up disk space or select a different output location"
        case .unsupportedFileFormat:
            return "Convert the file to a supported format or select a different file"
        case .compressionFailed:
            return "Check that the input file is not corrupted and try again"
        case .gpsExtractionFailed:
            return "The file may not contain GPS metadata or may be corrupted"
        case .gpsInjectionFailed:
            return "Ensure exiftool is installed and the output file is writable"
        case .fileSystemError:
            return "Check file permissions and available disk space"
        case .processExecutionError:
            return "Ensure all required tools are properly installed and accessible"
        }
    }
}

@MainActor
class ErrorHandler: ObservableObject {
    @Published var currentError: YeehawVideoError?
    @Published var showingErrorAlert = false
    @Published var errorLog: [ErrorLogEntry] = []
    
    struct ErrorLogEntry {
        let timestamp: Date
        let error: YeehawVideoError
        let context: String?
        let severity: Severity
        
        enum Severity {
            case warning, error, critical
            
            var icon: String {
                switch self {
                case .warning: return "exclamationmark.triangle.fill"
                case .error: return "xmark.circle.fill"
                case .critical: return "exclamationmark.octagon.fill"
                }
            }
            
            var color: Color {
                switch self {
                case .warning: return .orange
                case .error: return .red
                case .critical: return .purple
                }
            }
        }
    }
    
    func handle(_ error: YeehawVideoError, context: String? = nil, severity: ErrorLogEntry.Severity = .error) {
        let entry = ErrorLogEntry(
            timestamp: Date(),
            error: error,
            context: context,
            severity: severity
        )
        
        errorLog.append(entry)
        
        // Show alert for errors and critical issues
        if severity == .error || severity == .critical {
            currentError = error
            showingErrorAlert = true
        }
        
        // Log to console for debugging
        print("YeehawVideo Error [\(severity)]: \(error.localizedDescription)")
        if let context = context {
            print("Context: \(context)")
        }
        if let suggestion = error.recoverySuggestion {
            print("Suggestion: \(suggestion)")
        }
    }
    
    func clearErrors() {
        errorLog.removeAll()
        currentError = nil
        showingErrorAlert = false
    }
    
    func exportErrorLog() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        
        var logContent = "YeehawVideo Error Log\n"
        logContent += "Generated: \(dateFormatter.string(from: Date()))\n"
        logContent += "=" + String(repeating: "=", count: 50) + "\n\n"
        
        for entry in errorLog {
            logContent += "[\(dateFormatter.string(from: entry.timestamp))] [\(entry.severity)] \(entry.error.localizedDescription)\n"
            if let context = entry.context {
                logContent += "Context: \(context)\n"
            }
            if let suggestion = entry.error.recoverySuggestion {
                logContent += "Suggestion: \(suggestion)\n"
            }
            logContent += "\n"
        }
        
        return logContent
    }
}

// MARK: - Error Recovery Utilities

extension ErrorHandler {
    func validateDependencies() async -> [YeehawVideoError] {
        let requiredTools = ["brew", "ffmpeg", "jq", "exiftool"]
        var errors: [YeehawVideoError] = []
        
        for tool in requiredTools {
            let isInstalled = await checkToolInstalled(tool)
            if !isInstalled {
                errors.append(.dependencyNotFound(tool))
            }
        }
        
        return errors
    }
    
    func validateInputPath(_ path: String) -> YeehawVideoError? {
        guard !path.isEmpty else {
            return .invalidInputPath
        }
        
        var isDirectory: ObjCBool = false
        guard FileManager.default.fileExists(atPath: path, isDirectory: &isDirectory) else {
            return .invalidInputPath
        }
        
        if !isDirectory.boolValue {
            // Single file - check if it's a supported format
            let fileExtension = URL(fileURLWithPath: path).pathExtension.lowercased()
            let supportedExtensions = ["mov", "mp4", "webm", "avi", "mkv"]
            
            if !supportedExtensions.contains(fileExtension) {
                return .unsupportedFileFormat(fileExtension)
            }
        }
        
        return nil
    }
    
    func validateOutputPath(_ path: String) -> YeehawVideoError? {
        guard !path.isEmpty else {
            return .invalidOutputPath
        }
        
        var isDirectory: ObjCBool = false
        guard FileManager.default.fileExists(atPath: path, isDirectory: &isDirectory),
              isDirectory.boolValue else {
            return .invalidOutputPath
        }
        
        // Test write permissions
        let testFilePath = URL(fileURLWithPath: path).appendingPathComponent(".yeehaw_write_test").path
        let testData = "test".data(using: .utf8)!
        
        do {
            try testData.write(to: URL(fileURLWithPath: testFilePath))
            try FileManager.default.removeItem(atPath: testFilePath)
        } catch {
            return .invalidOutputPath
        }
        
        return nil
    }
    
    func checkDiskSpace(outputPath: String, estimatedSize: Int64) -> YeehawVideoError? {
        do {
            let attributes = try FileManager.default.attributesOfFileSystem(forPath: outputPath)
            let availableSpace = attributes[.systemFreeSize] as? Int64 ?? 0
            
            // Add 1GB buffer for safety
            let requiredSpace = estimatedSize + (1024 * 1024 * 1024)
            
            if availableSpace < requiredSpace {
                return .insufficientDiskSpace(required: requiredSpace, available: availableSpace)
            }
        } catch {
            return .fileSystemError("Could not check available disk space: \(error.localizedDescription)")
        }
        
        return nil
    }
    
    private func checkToolInstalled(_ tool: String) async -> Bool {
        return await withCheckedContinuation { continuation in
            let process = Process()
            process.executableURL = URL(fileURLWithPath: "/usr/bin/which")
            process.arguments = [tool]
            
            let pipe = Pipe()
            process.standardOutput = pipe
            process.standardError = pipe
            
            do {
                try process.run()
                process.waitUntilExit()
                continuation.resume(returning: process.terminationStatus == 0)
            } catch {
                continuation.resume(returning: false)
            }
        }
    }
}

// MARK: - SwiftUI Error Alert Modifier

struct ErrorAlertModifier: ViewModifier {
    @ObservedObject var errorHandler: ErrorHandler
    
    func body(content: Content) -> some View {
        content
            .alert("Error", isPresented: $errorHandler.showingErrorAlert) {
                Button("OK") {
                    errorHandler.currentError = nil
                }
                
                if let error = errorHandler.currentError,
                   let suggestion = error.recoverySuggestion {
                    Button("Help") {
                        // Could open help documentation or show more detailed recovery steps
                        print("Recovery suggestion: \(suggestion)")
                    }
                }
            } message: {
                if let error = errorHandler.currentError {
                    VStack(alignment: .leading) {
                        Text(error.localizedDescription)
                        
                        if let suggestion = error.recoverySuggestion {
                            Text("\nSuggestion: \(suggestion)")
                                .font(.caption)
                        }
                    }
                }
            }
    }
}

extension View {
    func errorAlert(_ errorHandler: ErrorHandler) -> some View {
        modifier(ErrorAlertModifier(errorHandler: errorHandler))
    }
}
